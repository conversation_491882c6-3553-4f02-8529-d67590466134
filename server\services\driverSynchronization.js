/**
 * Driver Synchronization Service
 * Handles automatic synchronization between Shift Management and Assignment Management
 * Ensures seamless driver transitions without disrupting active operations
 */

const { Pool } = require('pg');

// Database connection
const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT,
});

class DriverSynchronizationService {
  constructor() {
    this.isRunning = false;
    this.syncInterval = null;
    this.syncIntervalMs = 30000; // 30 seconds
    this.consecutiveFailures = 0;
    this.maxConsecutiveFailures = 5;
    this.backoffMultiplier = 1;
  }

  /**
   * Start automatic synchronization monitoring with enhanced error handling
   */
  startAutoSync() {
    if (this.isRunning) {
      console.log('🔄 Driver synchronization already running');
      return;
    }

    console.log('🚀 Starting driver synchronization service');
    this.isRunning = true;
    this.consecutiveFailures = 0;
    this.backoffMultiplier = 1;
    
    // Run initial synchronization
    this.performSynchronizationWithBackoff();
    
    // Set up periodic synchronization with adaptive backoff
    this.syncInterval = setInterval(() => {
      this.performSynchronizationWithBackoff();
    }, this.syncIntervalMs * this.backoffMultiplier);
  }

  /**
   * Stop automatic synchronization monitoring
   */
  stopAutoSync() {
    if (!this.isRunning) {
      return;
    }

    console.log('🛑 Stopping driver synchronization service');
    this.isRunning = false;
    
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
  }

  /**
   * Enhanced synchronization with adaptive backoff and circuit breaker
   */
  async performSynchronizationWithBackoff() {
    try {
      const result = await this.performSynchronization();
      
      // Reset failure counter on success
      if (result.success) {
        this.consecutiveFailures = 0;
        this.backoffMultiplier = 1;
        
        // Restart normal interval if it was increased
        if (this.syncInterval && this.backoffMultiplier > 1) {
          this.restartNormalInterval();
        }
      }
      
      return result;
    } catch (error) {
      this.consecutiveFailures++;
      
      // Circuit breaker: stop if too many consecutive failures
      if (this.consecutiveFailures >= this.maxConsecutiveFailures) {
        console.error('🚨 Circuit breaker activated - stopping synchronization due to repeated failures');
        this.stopAutoSync();
        return {
          success: false,
          message: 'Synchronization stopped due to repeated connection failures',
          error: error,
          circuit_breaker: true
        };
      }
      
      // Increase backoff multiplier
      this.backoffMultiplier = Math.min(Math.pow(2, this.consecutiveFailures - 1), 16);
      
      console.warn(`⚠️ Synchronization failed (${this.consecutiveFailures}/${this.maxConsecutiveFailures}), backoff: ${this.backoffMultiplier}x`);
      
      return {
        success: false,
        message: `Synchronization failed (attempt ${this.consecutiveFailures}/${this.maxConsecutiveFailures})`,
        error: error,
        next_retry_in_ms: this.syncIntervalMs * this.backoffMultiplier
      };
    }
  }

  /**
   * Restart normal synchronization interval
   */
  restartNormalInterval() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }
    
    this.syncInterval = setInterval(() => {
      this.performSynchronizationWithBackoff();
    }, this.syncIntervalMs);
  }

  /**
   * Perform driver synchronization between shifts and assignments
   * Enhanced with ECONNRESET handling and retry logic
   */
  async performSynchronization() {
    const maxRetries = 3;
    let lastError = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      let client = null;
      
      try {
        // Acquire connection with timeout
        client = await this.acquireConnectionWithTimeout(10000);
        
        // Step 1: Identify trucks with active shifts requiring synchronization
        const trucksRequiringSync = await this.identifyTrucksRequiringSync(client);
        
        if (trucksRequiringSync.length === 0) {
          // Only log sync success if not suppressed
          if (process.env.SUPPRESS_SYNC_SUCCESS_MESSAGES !== 'true') {
            console.log('✅ All systems synchronized - no action required');
          }
          return { success: true, message: 'All systems synchronized', updatedTrucks: [] };
        }

        console.log(`🔄 Synchronizing ${trucksRequiringSync.length} trucks (attempt ${attempt}/${maxRetries})`);
        
        const syncResults = [];
        
        // Step 2: Process each truck requiring synchronization
        for (const truck of trucksRequiringSync) {
          const result = await this.synchronizeTruck(client, truck);
          syncResults.push(result);
        }
        
        // Step 3: Log synchronization summary
        const successCount = syncResults.filter(r => r.success).length;
        const failureCount = syncResults.filter(r => !r.success).length;
        
        console.log(`✅ Synchronization completed: ${successCount} success, ${failureCount} failures`);
        
        return {
          success: failureCount === 0,
          message: `Synchronized ${successCount} trucks`,
          results: syncResults
        };
        
      } catch (error) {
        lastError = error;
        
        // Handle ECONNRESET and other connection errors
        if (this.isConnectionError(error)) {
          console.error(`❌ Connection error on attempt ${attempt}/${maxRetries}:`, error.message);
          
          if (attempt < maxRetries) {
            const backoffDelay = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
            console.log(`⏳ Retrying in ${backoffDelay}ms...`);
            await new Promise(resolve => setTimeout(resolve, backoffDelay));
            continue;
          }
        }
        
        // Log detailed error for debugging
        console.error('❌ Driver synchronization failed:', {
          message: error.message,
          code: error.code,
          stack: error.stack,
          attempt: attempt
        });
        
        // Return graceful degradation response
        return {
          success: false,
          message: `Synchronization failed after ${attempt} attempts: ${error.message}`,
          error: {
            type: error.code || 'UNKNOWN_ERROR',
            message: error.message,
            attempts: attempt,
            maxRetries: maxRetries
          },
          degraded: true
        };
        
      } finally {
        if (client) {
          try {
            client.release();
          } catch (releaseError) {
            console.error('❌ Error releasing client:', releaseError.message);
          }
        }
      }
    }
    
    // Final error handling
    console.error('❌ Driver synchronization failed after all retry attempts:', lastError.message);
    return {
      success: false,
      message: `Synchronization failed after ${maxRetries} attempts: ${lastError.message}`,
      error: lastError,
      degraded: true
    };
  }

  /**
   * Check if error is a connection-related error
   */
  isConnectionError(error) {
    const connectionErrorCodes = [
      'ECONNRESET',
      'ECONNREFUSED',
      'ENOTFOUND',
      'ETIMEDOUT',
      '57P01', // admin_shutdown
      '57P02', // crash_shutdown
      '57P03', // cannot_connect_now
      '53300', // too_many_connections
      '53400'  // configuration_limit_exceeded
    ];
    
    return connectionErrorCodes.includes(error.code) ||
           error.message.includes('ECONNRESET') ||
           error.message.includes('connection') ||
           error.message.includes('socket');
  }

  /**
   * Acquire connection with timeout and retry logic
   */
  async acquireConnectionWithTimeout(timeoutMs = 10000) {
    const maxRetries = 3;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const client = await Promise.race([
          pool.connect(),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Connection timeout')), timeoutMs)
          )
        ]);
        
        return client;
      } catch (error) {
        if (attempt === maxRetries) {
          throw new Error(`Failed to acquire database connection after ${maxRetries} attempts: ${error.message}`);
        }
        
        const retryDelay = Math.min(500 * attempt, 2000);
        console.warn(`⚠️ Connection acquisition failed (attempt ${attempt}/${maxRetries}), retrying in ${retryDelay}ms...`);
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }
    }
  }

  /**
   * Identify trucks that require driver synchronization
   * Enhanced with connection validation and error handling
   */
  async identifyTrucksRequiringSync(client) {
    const query = `
      WITH active_shifts AS (
        SELECT
          ds.truck_id,
          dt.truck_number,
          ds.driver_id as shift_driver_id,
          d.full_name as shift_driver_name,
          d.employee_id as shift_employee_id,
          ds.shift_type,
          ds.status as shift_status
        FROM driver_shifts ds
        JOIN dump_trucks dt ON ds.truck_id = dt.id
        JOIN drivers d ON ds.driver_id = d.id
        WHERE ds.status = 'active'
          OR (
            CURRENT_DATE BETWEEN ds.start_date AND ds.end_date
            AND CURRENT_TIME BETWEEN ds.start_time AND
              CASE
                WHEN ds.end_time < ds.start_time
                THEN ds.end_time + interval '24 hours'
                ELSE ds.end_time
              END
          )
      ),
      assignment_check AS (
        SELECT
          a.truck_id,
          COUNT(*) as total_assignments,
          COUNT(CASE WHEN a.driver_id = ash.shift_driver_id THEN 1 END) as synchronized_assignments,
          COUNT(CASE WHEN a.driver_id != ash.shift_driver_id OR a.driver_id IS NULL THEN 1 END) as unsynchronized_assignments
        FROM assignments a
        JOIN active_shifts ash ON a.truck_id = ash.truck_id
        WHERE a.status IN ('assigned', 'in_progress')
        GROUP BY a.truck_id, ash.shift_driver_id
      )
      SELECT
        ash.truck_id,
        ash.truck_number,
        ash.shift_driver_id,
        ash.shift_driver_name,
        ash.shift_employee_id,
        ash.shift_type,
        COALESCE(ac.total_assignments, 0) as total_assignments,
        COALESCE(ac.unsynchronized_assignments, 0) as unsynchronized_assignments
      FROM active_shifts ash
      LEFT JOIN assignment_check ac ON ash.truck_id = ac.truck_id
      WHERE COALESCE(ac.unsynchronized_assignments, 0) > 0
      ORDER BY ash.truck_number
    `;
    
    try {
      // Validate client connection before query
      if (!client || client._ending) {
        throw new Error('Invalid or closed database connection');
      }
      
      const result = await client.query(query);
      
      // Validate result
      if (!result || !Array.isArray(result.rows)) {
        throw new Error('Invalid query result format');
      }
      
      return result.rows;
    } catch (error) {
      console.error('❌ Error identifying trucks requiring sync:', {
        message: error.message,
        code: error.code,
        stack: error.stack
      });
      
      // Re-throw connection errors for retry handling
      if (this.isConnectionError(error)) {
        throw error;
      }
      
      // Return empty array for non-connection errors (graceful degradation)
      console.warn('⚠️ Returning empty sync list due to non-connection error');
      return [];
    }
  }

  /**
   * Synchronize a specific truck's assignments with active shift driver
   * Enhanced with transaction safety and error handling
   */
  async synchronizeTruck(client, truck) {
    const maxRetries = 2;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔄 Synchronizing ${truck.truck_number}: ${truck.shift_driver_name} (${truck.shift_type}) - attempt ${attempt}/${maxRetries}`);
        
        // Step 1: Check for active trips (4-phase workflow protection)
        const activeTripsQuery = `
          SELECT
            tl.id,
            tl.trip_number,
            tl.status,
            a.assignment_code
          FROM trip_logs tl
          JOIN assignments a ON tl.assignment_id = a.id
          WHERE a.truck_id = $1
            AND tl.status IN ('loading_start', 'loading_end', 'unloading_start', 'unloading_end')
        `;
        
        const activeTripsResult = await client.query(activeTripsQuery, [truck.truck_id]);
        
        if (activeTripsResult.rows.length > 0) {
          console.log(`🔒 Protected: ${activeTripsResult.rows.length} active trips found - updating assignments only`);
        }
        
        // Step 2: Update assignments with new driver
        const updateQuery = `
          UPDATE assignments
          SET
            driver_id = $1,
            updated_at = CURRENT_TIMESTAMP
          WHERE truck_id = $2
            AND status IN ('assigned', 'in_progress')
            AND (driver_id IS NULL OR driver_id != $1)
        `;
        
        const updateResult = await client.query(updateQuery, [truck.shift_driver_id, truck.truck_id]);
        
        console.log(`✅ ${truck.truck_number}: Updated ${updateResult.rowCount} assignments to ${truck.shift_driver_name}`);
        
        return {
          success: true,
          truck_number: truck.truck_number,
          driver_name: truck.shift_driver_name,
          assignments_updated: updateResult.rowCount,
          active_trips_protected: activeTripsResult.rows.length,
          attempts: attempt
        };
        
      } catch (error) {
        console.error(`❌ Failed to synchronize ${truck.truck_number} (attempt ${attempt}/${maxRetries}):`, {
          message: error.message,
          code: error.code
        });
        
        if (this.isConnectionError(error) && attempt < maxRetries) {
          const retryDelay = 500 * attempt;
          console.log(`⏳ Retrying truck sync in ${retryDelay}ms...`);
          await new Promise(resolve => setTimeout(resolve, retryDelay));
          continue;
        }
        
        return {
          success: false,
          truck_number: truck.truck_number,
          error: error.message,
          attempts: attempt
        };
      }
    }
  }

  /**
   * Manual synchronization trigger for specific truck
   */
  async syncSpecificTruck(truckId) {
    const client = await pool.connect();
    
    try {
      const trucksRequiringSync = await this.identifyTrucksRequiringSync(client);
      const targetTruck = trucksRequiringSync.find(truck => truck.truck_id === truckId);
      
      if (!targetTruck) {
        return {
          success: false,
          message: 'Truck does not require synchronization or has no active shift'
        };
      }
      
      const result = await this.synchronizeTruck(client, targetTruck);
      return result;
      
    } catch (error) {
      return {
        success: false,
        message: `Manual synchronization failed: ${error.message}`,
        error: error
      };
    } finally {
      client.release();
    }
  }

  /**
   * Get synchronization status for all trucks
   */
  async getSynchronizationStatus() {
    const client = await pool.connect();
    
    try {
      const query = `
        WITH active_shifts AS (
          SELECT 
            ds.truck_id,
            dt.truck_number,
            ds.driver_id as shift_driver_id,
            d.full_name as shift_driver_name,
            ds.shift_type
          FROM driver_shifts ds
          JOIN dump_trucks dt ON ds.truck_id = dt.id
          JOIN drivers d ON ds.driver_id = d.id
          WHERE ds.status = 'active'
            OR (
              CURRENT_DATE BETWEEN ds.start_date AND ds.end_date
              AND CURRENT_TIME BETWEEN ds.start_time AND
                CASE
                  WHEN ds.end_time < ds.start_time
                  THEN ds.end_time + interval '24 hours'
                  ELSE ds.end_time
                END
            )
        ),
        assignment_drivers AS (
          SELECT 
            a.truck_id,
            COUNT(DISTINCT a.driver_id) as unique_drivers,
            STRING_AGG(DISTINCT d.full_name, ', ') as assignment_driver_names
          FROM assignments a
          LEFT JOIN drivers d ON a.driver_id = d.id
          WHERE a.status IN ('assigned', 'in_progress')
          GROUP BY a.truck_id
        )
        SELECT 
          dt.truck_number,
          ash.shift_driver_name,
          ash.shift_type,
          ad.assignment_driver_names,
          ad.unique_drivers,
          CASE 
            WHEN ash.shift_driver_id IS NULL THEN 'NO_ACTIVE_SHIFT'
            WHEN ad.assignment_driver_names IS NULL THEN 'NO_ASSIGNMENTS'
            WHEN ad.unique_drivers = 1 AND ad.assignment_driver_names = ash.shift_driver_name 
            THEN 'SYNCHRONIZED'
            ELSE 'OUT_OF_SYNC'
          END as sync_status
        FROM dump_trucks dt
        LEFT JOIN active_shifts ash ON dt.id = ash.truck_id
        LEFT JOIN assignment_drivers ad ON dt.id = ad.truck_id
        WHERE dt.status = 'active'
        ORDER BY dt.truck_number
      `;
      
      const result = await client.query(query);
      return {
        success: true,
        trucks: result.rows
      };
      
    } catch (error) {
      return {
        success: false,
        message: `Failed to get synchronization status: ${error.message}`,
        error: error
      };
    } finally {
      client.release();
    }
  }
}

// Export singleton instance
const driverSyncService = new DriverSynchronizationService();
module.exports = driverSyncService;
