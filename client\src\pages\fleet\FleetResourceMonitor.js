import React, { useState, useEffect, useCallback } from 'react';
import axios from 'axios';
import { format, startOfDay, endOfDay } from 'date-fns';
import ResourceSummaryCards from '../../components/fleet/ResourceSummaryCards';
import DateRangePicker from '../../components/common/DateRangePicker';
import { getApiBaseUrl } from '../../utils/network-utils';

const FleetResourceMonitor = () => {
  const [resourceData, setResourceData] = useState(null);
  const [loadingLocationData, setLoadingLocationData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [expandedLocations, setExpandedLocations] = useState(new Set());
  const [dateRange, setDateRange] = useState({
    from: startOfDay(new Date()),
    to: endOfDay(new Date())
  });
  const [isDateFiltered, setIsDateFiltered] = useState(false);

  // Fetch resource data from API with date filtering
  const fetchResourceData = useCallback(async (customDateRange = null) => {
    try {
      setLoading(true);
      const token = localStorage.getItem('hauling_token');
      const apiUrl = getApiBaseUrl();

      // Use provided date range or current state
      const currentDateRange = customDateRange || dateRange;

      // Format dates for API
      const dateParams = {};
      if (currentDateRange.from && currentDateRange.to) {
        dateParams.date_from = format(currentDateRange.from, 'yyyy-MM-dd');
        dateParams.date_to = format(currentDateRange.to, 'yyyy-MM-dd');
      }

      // Fetch both resource summary and loading location data
      const [summaryResponse, locationResponse] = await Promise.all([
        axios.get(`${apiUrl}/fleet-resources/summary`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          params: dateParams
        }),
        axios.get(`${apiUrl}/fleet-resources/loading-locations`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          params: dateParams
        })
      ]);

      if (summaryResponse.data.success && locationResponse.data.success) {
        setResourceData(summaryResponse.data.data);
        setLoadingLocationData(locationResponse.data.data.locations);
        setLastUpdated(new Date());
        setError(null);

        // Update filtered state
        const today = new Date();
        const isToday = currentDateRange.from && currentDateRange.to &&
                       format(currentDateRange.from, 'yyyy-MM-dd') === format(today, 'yyyy-MM-dd') &&
                       format(currentDateRange.to, 'yyyy-MM-dd') === format(today, 'yyyy-MM-dd');
        setIsDateFiltered(!isToday);
      } else {
        setError('Failed to fetch resource data');
      }
    } catch (err) {
      console.error('Error fetching resource data:', err);
      setError(err.response?.data?.error?.message || 'Failed to fetch resource data');
    } finally {
      setLoading(false);
    }
  }, [dateRange]);

  // Initial data fetch
  useEffect(() => {
    fetchResourceData();
  }, []);

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(fetchResourceData, 30000);
    return () => clearInterval(interval);
  }, []);

  // Toggle expanded state for loading locations
  const toggleLocationExpansion = (locationName) => {
    const newExpanded = new Set(expandedLocations);
    if (newExpanded.has(locationName)) {
      newExpanded.delete(locationName);
    } else {
      newExpanded.add(locationName);
    }
    setExpandedLocations(newExpanded);
  };

  if (loading && !resourceData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading fleet resource data...</p>
        </div>
      </div>
    );
  }

  if (error && !resourceData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            <h2 className="font-bold">Error Loading Fleet Resource Monitor</h2>
            <p>{error}</p>
            <button 
              onClick={fetchResourceData}
              className="mt-2 bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  🚛 Fleet Resource Monitor
                </h1>
                <p className="mt-2 text-gray-600">
                  Real-time monitoring of driver and dump truck resources
                </p>
              </div>
              <div className="text-right">
                <button
                  onClick={fetchResourceData}
                  disabled={loading}
                  className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
                >
                  {loading ? 'Refreshing...' : 'Refresh'}
                </button>
                {lastUpdated && (
                  <p className="text-sm text-gray-500 mt-1">
                    Last updated: {lastUpdated.toLocaleTimeString()}
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {resourceData ? (
          <div className="space-y-8">
            {/* Resource Summary Cards */}
            <ResourceSummaryCards resourceData={resourceData} />

            {/* Loading Location Breakdown */}
            {loadingLocationData && loadingLocationData.length > 0 && (
              <div className="bg-white shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                    🏗️ Loading Location Assignments
                  </h3>
                  <div className="space-y-4">
                    {loadingLocationData.map((location) => (
                      <div key={location.location_name} className="border border-gray-200 rounded-lg">
                        {/* Location Summary Header */}
                        <div
                          className="px-4 py-3 bg-gray-50 cursor-pointer hover:bg-gray-100 transition-colors duration-150 flex items-center justify-between"
                          onClick={() => toggleLocationExpansion(location.location_name)}
                        >
                          <div className="flex items-center space-x-3">
                            <span className="text-2xl">📍</span>
                            <div>
                              <h4 className="text-sm font-medium text-gray-900">
                                {location.location_name}
                              </h4>
                              <p className="text-xs text-gray-500">
                                {location.total_assigned} truck{location.total_assigned !== 1 ? 's' : ''} assigned
                                {location.on_route > 0 && (
                                  <span className="ml-2 text-blue-600">
                                    • {location.on_route} on route
                                  </span>
                                )}
                                {location.currently_at_location > 0 && (
                                  <span className="ml-2 text-green-600">
                                    • {location.currently_at_location} at location
                                  </span>
                                )}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                              {location.total_assigned} trucks
                            </span>
                            <svg
                              className={`w-5 h-5 text-gray-400 transform transition-transform duration-200 ${
                                expandedLocations.has(location.location_name) ? 'rotate-180' : ''
                              }`}
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                            </svg>
                          </div>
                        </div>

                        {/* Expanded Truck Details */}
                        {expandedLocations.has(location.location_name) && (
                          <div className="px-4 py-4 border-t border-gray-200">
                            <div className="overflow-hidden">
                              <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                  <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                      Truck Number
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                      Driver
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                      Status
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                      Current Trip
                                    </th>
                                  </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                  {location.assigned_trucks.map((truck) => (
                                    <tr key={`${location.location_name}-${truck.truck_id}`}>
                                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        <div className="flex items-center">
                                          <span className="text-2xl mr-2">🚚</span>
                                          {truck.truck_number}
                                        </div>
                                      </td>
                                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {truck.driver_name || 'Unassigned'}
                                      </td>
                                      <td className="px-6 py-4 whitespace-nowrap">
                                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                          truck.status === 'on_route'
                                            ? 'bg-blue-100 text-blue-800'
                                            : 'bg-green-100 text-green-800'
                                        }`}>
                                          {truck.status === 'on_route' ? '🚛 On Route' : '📍 At Location'}
                                        </span>
                                      </td>
                                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {truck.current_trip ? (
                                          <div>
                                            <div className="text-xs font-medium">
                                              → {truck.current_trip.unloading_location}
                                            </div>
                                            <div className="text-xs text-gray-400">
                                              Started: {new Date(truck.current_trip.trip_start).toLocaleTimeString()}
                                            </div>
                                          </div>
                                        ) : (
                                          <span className="text-gray-400">No active trip</span>
                                        )}
                                      </td>
                                    </tr>
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Detailed Information */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Unassigned Drivers */}
              {resourceData.drivers.unassigned_details.length > 0 && (
                <div className="bg-white shadow rounded-lg">
                  <div className="px-4 py-5 sm:p-6">
                    <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                      Unassigned Drivers ({resourceData.drivers.unassigned_details.length})
                    </h3>
                    <div className="overflow-hidden">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Driver
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Employee ID
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Status
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {resourceData.drivers.unassigned_details.map((driver) => (
                            <tr key={driver.driver_id}>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {driver.full_name}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {driver.employee_id}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                  Available
                                </span>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              )}

              {/* Unassigned Trucks */}
              {resourceData.trucks.unassigned_details.length > 0 && (
                <div className="bg-white shadow rounded-lg">
                  <div className="px-4 py-5 sm:p-6">
                    <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                      Unassigned Trucks ({resourceData.trucks.unassigned_details.length})
                    </h3>
                    <div className="overflow-hidden">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Truck Number
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              License Plate
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Status
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {resourceData.trucks.unassigned_details.map((truck) => (
                            <tr key={truck.truck_id}>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {truck.truck_number}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {truck.license_plate}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                  Available
                                </span>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Empty State */}
            {resourceData.drivers.unassigned_details.length === 0 &&
             resourceData.trucks.unassigned_details.length === 0 &&
             (!loadingLocationData || loadingLocationData.length === 0) && (
              <div className="text-center py-12">
                <div className="text-gray-400 text-6xl mb-4">📊</div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">All Resources Assigned</h3>
                <p className="text-gray-500">
                  All available drivers and trucks are currently assigned to active shifts.
                </p>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">⚠️</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Data Available</h3>
            <p className="text-gray-500">Unable to load fleet resource data.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default FleetResourceMonitor;