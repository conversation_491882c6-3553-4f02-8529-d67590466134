import React from 'react';
import DatePicker from 'react-datepicker';
import { format, subDays, startOfDay, endOfDay } from 'date-fns';
import "react-datepicker/dist/react-datepicker.css";

const DateRangePicker = ({ 
  dateRange, 
  onDateRangeChange, 
  loading = false,
  className = "" 
}) => {
  const { from, to } = dateRange;

  // Quick select options
  const quickSelectOptions = [
    {
      label: 'Today',
      getValue: () => ({
        from: startOfDay(new Date()),
        to: endOfDay(new Date())
      })
    },
    {
      label: 'Yesterday',
      getValue: () => ({
        from: startOfDay(subDays(new Date(), 1)),
        to: endOfDay(subDays(new Date(), 1))
      })
    },
    {
      label: 'Last 7 Days',
      getValue: () => ({
        from: startOfDay(subDays(new Date(), 6)),
        to: endOfDay(new Date())
      })
    },
    {
      label: 'Last 30 Days',
      getValue: () => ({
        from: startOfDay(subDays(new Date(), 29)),
        to: endOfDay(new Date())
      })
    }
  ];

  const handleFromDateChange = (date) => {
    if (date) {
      onDateRangeChange({
        from: startOfDay(date),
        to: to || endOfDay(date)
      });
    }
  };

  const handleToDateChange = (date) => {
    if (date) {
      onDateRangeChange({
        from: from || startOfDay(date),
        to: endOfDay(date)
      });
    }
  };

  const handleQuickSelect = (option) => {
    onDateRangeChange(option.getValue());
  };

  const isToday = () => {
    const today = new Date();
    return from && to && 
           format(from, 'yyyy-MM-dd') === format(today, 'yyyy-MM-dd') &&
           format(to, 'yyyy-MM-dd') === format(today, 'yyyy-MM-dd');
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-4 ${className}`}>
      {/* Date Range Display */}
      <div className="mb-4">
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium text-gray-700">Showing data for:</span>
          <span className="text-sm font-semibold text-blue-600">
            {from && to ? (
              format(from, 'yyyy-MM-dd') === format(to, 'yyyy-MM-dd') 
                ? format(from, 'MMM dd, yyyy')
                : `${format(from, 'MMM dd, yyyy')} - ${format(to, 'MMM dd, yyyy')}`
            ) : 'All time'}
          </span>
          {!isToday() && (
            <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
              Filtered
            </span>
          )}
          {loading && (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          )}
        </div>
      </div>

      {/* Date Inputs */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">
            From Date
          </label>
          <DatePicker
            selected={from}
            onChange={handleFromDateChange}
            selectsStart
            startDate={from}
            endDate={to}
            maxDate={new Date()}
            dateFormat="MMM dd, yyyy"
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
            placeholderText="Select start date"
            disabled={loading}
          />
        </div>
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">
            To Date
          </label>
          <DatePicker
            selected={to}
            onChange={handleToDateChange}
            selectsEnd
            startDate={from}
            endDate={to}
            minDate={from}
            maxDate={new Date()}
            dateFormat="MMM dd, yyyy"
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
            placeholderText="Select end date"
            disabled={loading}
          />
        </div>
      </div>

      {/* Quick Select Buttons */}
      <div className="flex flex-wrap gap-2">
        {quickSelectOptions.map((option) => (
          <button
            key={option.label}
            onClick={() => handleQuickSelect(option)}
            disabled={loading}
            className="px-3 py-1 text-xs font-medium text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-150"
          >
            {option.label}
          </button>
        ))}
        <button
          onClick={() => handleQuickSelect(quickSelectOptions[0])} // Reset to Today
          disabled={loading || isToday()}
          className="px-3 py-1 text-xs font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-150"
        >
          Reset to Today
        </button>
      </div>
    </div>
  );
};

export default DateRangePicker;
