/**
 * Log Management API Routes
 * 
 * Provides endpoints for monitoring and managing the logging system
 */

const express = require('express');
const router = express.Router();
const LogManagement = require('../utils/log-management');
const { logInfo, logError, logDeduplicator, asyncLogBuffer } = require('../utils/logger');
const { authenticateToken, requireRole } = require('../middleware/auth');

// Initialize log management
const logManager = new LogManagement();

/**
 * GET /api/log-management/stats
 * Get comprehensive log file statistics
 */
router.get('/stats', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const stats = await logManager.getLogStats();
    const bufferStats = logManager.getBufferStats();
    const deduplicationStats = logDeduplicator.getStats();
    
    res.json({
      success: true,
      data: {
        files: stats,
        buffer: bufferStats,
        deduplication: deduplicationStats,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logError('LOG_MANAGEMENT_STATS_ERROR', error, {
      user_id: req.user?.id,
      ip_address: req.ip
    });
    
    res.status(500).json({
      success: false,
      error: 'Log Stats Error',
      message: 'Failed to retrieve log statistics'
    });
  }
});

/**
 * GET /api/log-management/analysis
 * Analyze log patterns and provide optimization recommendations
 */
router.get('/analysis', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const analysis = await logManager.analyzeLogPatterns();
    
    if (analysis.error) {
      return res.status(404).json({
        success: false,
        error: 'Analysis Error',
        message: analysis.error
      });
    }
    
    res.json({
      success: true,
      data: analysis
    });
  } catch (error) {
    logError('LOG_MANAGEMENT_ANALYSIS_ERROR', error, {
      user_id: req.user?.id,
      ip_address: req.ip
    });
    
    res.status(500).json({
      success: false,
      error: 'Log Analysis Error',
      message: 'Failed to analyze log patterns'
    });
  }
});

/**
 * POST /api/log-management/cleanup
 * Clean up old log files based on retention policy
 */
router.post('/cleanup', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { maxAgeDays = 7, maxFiles = 50 } = req.body;
    const maxAgeMs = maxAgeDays * 24 * 60 * 60 * 1000;
    
    const result = await logManager.cleanupOldLogs(maxAgeMs, maxFiles);
    
    if (!result) {
      return res.status(500).json({
        success: false,
        error: 'Cleanup Error',
        message: 'Failed to clean up log files'
      });
    }
    
    logInfo('LOG_MANAGEMENT_CLEANUP', 'Log cleanup completed', {
      user_id: req.user.id,
      username: req.user.username,
      deleted_files: result.deletedFiles,
      deleted_size: result.deletedSizeHuman,
      max_age_days: maxAgeDays,
      max_files: maxFiles
    });
    
    res.json({
      success: true,
      message: 'Log cleanup completed successfully',
      data: result
    });
  } catch (error) {
    logError('LOG_MANAGEMENT_CLEANUP_ERROR', error, {
      user_id: req.user?.id,
      ip_address: req.ip
    });
    
    res.status(500).json({
      success: false,
      error: 'Log Cleanup Error',
      message: 'Failed to clean up log files'
    });
  }
});

/**
 * POST /api/log-management/flush-buffer
 * Force flush the async log buffer
 */
router.post('/flush-buffer', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const flushed = await logManager.flushBuffer();
    
    res.json({
      success: true,
      message: flushed ? 'Log buffer flushed successfully' : 'No buffer to flush',
      data: {
        bufferFlushed: flushed,
        bufferStats: logManager.getBufferStats()
      }
    });
  } catch (error) {
    logError('LOG_MANAGEMENT_FLUSH_ERROR', error, {
      user_id: req.user?.id,
      ip_address: req.ip
    });
    
    res.status(500).json({
      success: false,
      error: 'Buffer Flush Error',
      message: 'Failed to flush log buffer'
    });
  }
});

/**
 * GET /api/log-management/health
 * Get logging system health status
 */
router.get('/health', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const stats = await logManager.getLogStats();
    const bufferStats = logManager.getBufferStats();
    const deduplicationStats = logDeduplicator.getStats();
    
    // Determine health status
    const totalSizeMB = stats ? stats.totalSize / (1024 * 1024) : 0;
    const isHealthy = totalSizeMB < 100; // Consider unhealthy if logs > 100MB
    
    const health = {
      status: isHealthy ? 'healthy' : 'warning',
      totalLogSizeMB: Math.round(totalSizeMB * 100) / 100,
      totalFiles: stats ? stats.totalFiles : 0,
      bufferActive: !!bufferStats,
      deduplicationActive: !!deduplicationStats,
      recommendations: []
    };
    
    if (totalSizeMB > 100) {
      health.recommendations.push('Log files are large. Consider running cleanup.');
    }
    
    if (bufferStats && bufferStats.currentBufferSize > 50) {
      health.recommendations.push('Log buffer is large. Consider flushing.');
    }
    
    if (deduplicationStats && deduplicationStats.cachedMessages > 1000) {
      health.recommendations.push('Deduplication cache is large. System may be generating many duplicate messages.');
    }
    
    res.json({
      success: true,
      data: health
    });
  } catch (error) {
    logError('LOG_MANAGEMENT_HEALTH_ERROR', error, {
      user_id: req.user?.id,
      ip_address: req.ip
    });
    
    res.status(500).json({
      success: false,
      error: 'Log Health Error',
      message: 'Failed to check logging system health'
    });
  }
});

module.exports = router;
