const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const auth = require('../middleware/auth');
const FleetResourceQueries = require('../utils/FleetResourceQueries');

// @route   GET /api/fleet-resources/summary
// @desc    Get fleet resource summary with driver and truck counts
// @access  Private
router.get('/summary', auth, async (req, res) => {
  try {
    // Get driver and truck resource summaries from database
    const [driverData, truckData] = await Promise.all([
      FleetResourceQueries.getDriverResourceSummary(),
      FleetResourceQueries.getTruckResourceSummary()
    ]);
    
    // Calculate alerts based on actual data
    const alerts = FleetResourceQueries.calculateAlerts(driverData, truckData);
    
    const summary = {
      drivers: driverData,
      trucks: truckData,
      alerts: alerts
    };

    res.json({
      success: true,
      data: summary
    });
  } catch (error) {
    console.error('Fleet Resource Summary Error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to fetch fleet resource summary',
        details: error.message
      }
    });
  }
});

// @route   GET /api/fleet-resources/loading-locations
// @desc    Get trucks grouped by loading location
// @access  Private
router.get('/loading-locations', auth, async (req, res) => {
  try {
    const locations = await FleetResourceQueries.getLoadingLocationBreakdown();

    res.json({
      success: true,
      data: {
        locations: locations
      }
    });
  } catch (error) {
    console.error('Loading Locations Error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to fetch loading location data',
        details: error.message
      }
    });
  }
});





// @route   GET /api/fleet-resources/utilization-history
// @desc    Get historical utilization data for trends
// @access  Private
router.get('/utilization-history', auth, async (req, res) => {
  try {
    const { days = 7 } = req.query;
    
    const history = await FleetResourceQueries.getUtilizationHistory(parseInt(days));

    res.json({
      success: true,
      data: history
    });
  } catch (error) {
    console.error('Utilization History Error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to fetch utilization history',
        details: error.message
      }
    });
  }
});

// Test endpoint to check trucks on route
router.get('/test-on-route', auth, async (req, res) => {
  try {
    const trucksOnRoute = await FleetResourceQueries.getTrucksOnRoute();
    res.json({
      success: true,
      data: {
        trucks_on_route: trucksOnRoute,
        count: trucksOnRoute.length
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;